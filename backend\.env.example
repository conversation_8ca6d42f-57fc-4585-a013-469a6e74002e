# Environment Configuration
NODE_ENV=development

# Server Configuration
PORT=3000

# Database Configuration
# For local development (MongoDB running locally)
# MONGODB_URI=mongodb://localhost:27017/portfoliox

# For MongoDB Atlas (cloud database) - replace with your connection string
MONGODB_URI=your-mongodb-connection-string-here

# Security Configuration
# Add your production domain here when deploying
ALLOWED_ORIGINS=https://yourdomain.com,http://localhost:3000,http://127.0.0.1:3000,http://localhost:5500

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
CONTACT_RATE_LIMIT_MAX=5

# Email Configuration
# SMTP Settings - Configure these to enable email notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=Your Name
TO_EMAIL=<EMAIL>

# Email Features
SEND_NOTIFICATION_EMAIL=true
SEND_CONFIRMATION_EMAIL=true
EMAIL_RATE_LIMIT_MAX=10
EMAIL_RATE_LIMIT_WINDOW_MS=3600000

# Email Provider Examples:
# 
# Gmail:
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_SECURE=false
# Note: Use App Password, not regular password
#
# Outlook/Hotmail:
# SMTP_HOST=smtp-mail.outlook.com
# SMTP_PORT=587
# SMTP_SECURE=false
#
# Yahoo:
# SMTP_HOST=smtp.mail.yahoo.com
# SMTP_PORT=587
# SMTP_SECURE=false
#
# SendGrid:
# SMTP_HOST=smtp.sendgrid.net
# SMTP_PORT=587
# SMTP_SECURE=false
# SMTP_USER=apikey
# SMTP_PASS=your-sendgrid-api-key
#
# Mailgun:
# SMTP_HOST=smtp.mailgun.org
# SMTP_PORT=587
# SMTP_SECURE=false
