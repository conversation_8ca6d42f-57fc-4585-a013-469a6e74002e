const nodemailer = require('nodemailer');
const fs = require('fs').promises;
const path = require('path');

class EmailService {
    constructor() {
        this.transporter = null;
        this.isConfigured = false;
        this.emailQueue = [];
        this.isProcessing = false;
        this.rateLimitMap = new Map();
        this.emailStats = {
            sent: 0,
            failed: 0,
            rateLimited: 0
        };

        this.initializeTransporter();
    }

    /**
     * Initialize the email transporter with SMTP configuration
     */
    async initializeTransporter() {
        try {
            // Check if email is enabled
            if (process.env.SEND_NOTIFICATION_EMAIL !== 'true' && process.env.SEND_CONFIRMATION_EMAIL !== 'true') {
                console.log('Email notifications are disabled');
                return;
            }

            // Validate required environment variables
            const requiredVars = ['SMTP_HOST', 'SMTP_PORT', 'SMTP_USER', 'SMTP_PASS', 'FROM_EMAIL', 'TO_EMAIL'];
            const missingVars = requiredVars.filter(varName => !process.env[varName]);
            
            if (missingVars.length > 0) {
                console.warn(`Email service not configured. Missing environment variables: ${missingVars.join(', ')}`);
                return;
            }

            // Create transporter
            this.transporter = nodemailer.createTransport({
                host: process.env.SMTP_HOST,
                port: parseInt(process.env.SMTP_PORT),
                secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
                auth: {
                    user: process.env.SMTP_USER,
                    pass: process.env.SMTP_PASS
                },
                // Additional security options
                tls: {
                    rejectUnauthorized: process.env.NODE_ENV === 'production'
                }
            });

            // Verify connection
            await this.transporter.verify();
            this.isConfigured = true;
            console.log('Email service initialized successfully');

        } catch (error) {
            console.error('Failed to initialize email service:', error.message);
            this.isConfigured = false;
        }
    }

    /**
     * Validate email address
     */
    validateEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email) && email.length <= 255;
    }

    /**
     * Sanitize email content to prevent injection
     */
    sanitizeContent(content) {
        if (typeof content !== 'string') return '';

        // Remove potentially dangerous HTML tags and scripts
        return content
            .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
            .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
            .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
            .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '')
            .replace(/javascript:/gi, '')
            .replace(/on\w+\s*=/gi, '')
            .trim();
    }

    /**
     * Check rate limiting for email sending
     */
    checkRateLimit(identifier) {
        const now = Date.now();
        const windowMs = parseInt(process.env.EMAIL_RATE_LIMIT_WINDOW_MS) || 3600000; // 1 hour default
        const maxEmails = parseInt(process.env.EMAIL_RATE_LIMIT_MAX) || 10;

        if (!this.rateLimitMap.has(identifier)) {
            this.rateLimitMap.set(identifier, []);
        }

        const timestamps = this.rateLimitMap.get(identifier);
        
        // Remove old timestamps outside the window
        const validTimestamps = timestamps.filter(timestamp => now - timestamp < windowMs);
        this.rateLimitMap.set(identifier, validTimestamps);

        // Check if limit exceeded
        if (validTimestamps.length >= maxEmails) {
            return false;
        }

        // Add current timestamp
        validTimestamps.push(now);
        return true;
    }

    /**
     * Load and process email template
     */
    async loadTemplate(templateName, variables = {}) {
        try {
            const templatePath = path.join(__dirname, '..', 'templates', `${templateName}.html`);
            let template = await fs.readFile(templatePath, 'utf8');

            // Replace variables in template
            Object.keys(variables).forEach(key => {
                const regex = new RegExp(`{{${key}}}`, 'g');
                template = template.replace(regex, variables[key] || '');
            });

            return template;
        } catch (error) {
            console.error(`Failed to load email template ${templateName}:`, error.message);
            return null;
        }
    }

    /**
     * Send notification email to portfolio owner
     */
    async sendNotificationEmail(contactData) {
        if (!this.isConfigured || process.env.SEND_NOTIFICATION_EMAIL !== 'true') {
            return { success: false, message: 'Notification emails are disabled' };
        }

        // Validate email addresses
        if (!this.validateEmail(contactData.email)) {
            console.warn('Invalid email address in contact data:', contactData.email);
            return { success: false, message: 'Invalid email address' };
        }

        if (!this.validateEmail(process.env.TO_EMAIL)) {
            console.warn('Invalid TO_EMAIL configuration:', process.env.TO_EMAIL);
            return { success: false, message: 'Invalid recipient email configuration' };
        }

        // Rate limiting check
        if (!this.checkRateLimit('notification')) {
            console.warn('Notification email rate limit exceeded');
            this.emailStats.rateLimited++;
            return { success: false, message: 'Rate limit exceeded' };
        }

        try {
            const template = await this.loadTemplate('notificationEmail', {
                name: this.sanitizeContent(contactData.name),
                email: contactData.email, // Don't sanitize email as it's already validated
                subject: this.sanitizeContent(contactData.subject),
                message: this.sanitizeContent(contactData.message),
                timestamp: new Date(contactData.createdAt).toLocaleString(),
                ipAddress: contactData.ipAddress || 'Unknown',
                userAgent: this.sanitizeContent(contactData.userAgent || 'Unknown'),
                isSpam: contactData.isSpam ? 'Yes' : 'No',
                spamDisplay: contactData.isSpam ? 'block' : 'none'
            });

            if (!template) {
                throw new Error('Failed to load notification email template');
            }

            const mailOptions = {
                from: `"${process.env.FROM_NAME}" <${process.env.FROM_EMAIL}>`,
                to: process.env.TO_EMAIL,
                subject: `New Contact Form Submission: ${contactData.subject}`,
                html: template,
                text: `New contact form submission from ${contactData.name} (${contactData.email})\n\nSubject: ${contactData.subject}\n\nMessage:\n${contactData.message}\n\nSubmitted at: ${new Date(contactData.createdAt).toLocaleString()}`
            };

            const result = await this.transporter.sendMail(mailOptions);
            console.log('Notification email sent successfully:', result.messageId);
            this.emailStats.sent++;

            return { success: true, messageId: result.messageId };

        } catch (error) {
            console.error('Failed to send notification email:', error.message);
            this.emailStats.failed++;
            return { success: false, message: error.message };
        }
    }

    /**
     * Send confirmation email to user
     */
    async sendConfirmationEmail(contactData) {
        if (!this.isConfigured || process.env.SEND_CONFIRMATION_EMAIL !== 'true') {
            return { success: false, message: 'Confirmation emails are disabled' };
        }

        // Validate email address
        if (!this.validateEmail(contactData.email)) {
            console.warn('Invalid email address for confirmation:', contactData.email);
            return { success: false, message: 'Invalid email address' };
        }

        // Rate limiting check (per email address)
        if (!this.checkRateLimit(contactData.email)) {
            console.warn(`Confirmation email rate limit exceeded for ${contactData.email}`);
            this.emailStats.rateLimited++;
            return { success: false, message: 'Rate limit exceeded' };
        }

        try {
            const template = await this.loadTemplate('confirmationEmail', {
                name: this.sanitizeContent(contactData.name),
                subject: this.sanitizeContent(contactData.subject),
                timestamp: new Date(contactData.createdAt).toLocaleString(),
                fromName: this.sanitizeContent(process.env.FROM_NAME || 'Pradeep Yadav')
            });

            if (!template) {
                throw new Error('Failed to load confirmation email template');
            }

            const mailOptions = {
                from: `"${process.env.FROM_NAME}" <${process.env.FROM_EMAIL}>`,
                to: contactData.email,
                subject: 'Thank you for contacting me!',
                html: template,
                text: `Hi ${contactData.name},\n\nThank you for reaching out! I have received your message about "${contactData.subject}" and will get back to you as soon as possible.\n\nBest regards,\n${process.env.FROM_NAME || 'Pradeep Yadav'}`
            };

            const result = await this.transporter.sendMail(mailOptions);
            console.log('Confirmation email sent successfully:', result.messageId);
            this.emailStats.sent++;

            return { success: true, messageId: result.messageId };

        } catch (error) {
            console.error('Failed to send confirmation email:', error.message);
            this.emailStats.failed++;
            return { success: false, message: error.message };
        }
    }

    /**
     * Send both notification and confirmation emails
     */
    async sendEmails(contactData) {
        const results = {
            notification: { success: false },
            confirmation: { success: false }
        };

        // Send notification email (don't await to avoid blocking)
        if (process.env.SEND_NOTIFICATION_EMAIL === 'true') {
            this.sendNotificationEmail(contactData)
                .then(result => {
                    results.notification = result;
                })
                .catch(error => {
                    console.error('Notification email error:', error);
                    results.notification = { success: false, message: error.message };
                });
        }

        // Send confirmation email (don't await to avoid blocking)
        if (process.env.SEND_CONFIRMATION_EMAIL === 'true') {
            this.sendConfirmationEmail(contactData)
                .then(result => {
                    results.confirmation = result;
                })
                .catch(error => {
                    console.error('Confirmation email error:', error);
                    results.confirmation = { success: false, message: error.message };
                });
        }

        return results;
    }

    /**
     * Test email configuration
     */
    async testConfiguration() {
        if (!this.isConfigured) {
            return { success: false, message: 'Email service not configured' };
        }

        try {
            await this.transporter.verify();
            return { success: true, message: 'Email configuration is valid' };
        } catch (error) {
            return { success: false, message: error.message };
        }
    }

    /**
     * Get email service statistics
     */
    getStats() {
        return {
            ...this.emailStats,
            isConfigured: this.isConfigured,
            rateLimitEntries: this.rateLimitMap.size,
            features: {
                notificationEmail: process.env.SEND_NOTIFICATION_EMAIL === 'true',
                confirmationEmail: process.env.SEND_CONFIRMATION_EMAIL === 'true'
            }
        };
    }

    /**
     * Reset email statistics
     */
    resetStats() {
        this.emailStats = {
            sent: 0,
            failed: 0,
            rateLimited: 0
        };
    }
}

// Create singleton instance
const emailService = new EmailService();

module.exports = emailService;
